import os
import sys
import json
import pickle
import logging
import numpy as np
import scipy.sparse as sp
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score, f1_score
from pathlib import Path
from typing import Dict
import gc

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))
from src.models.optimized_tagt import create_optimized_model

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('results/cross_validation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedSLEDataset(Dataset):
    """Memory-efficient SLE dataset loader."""
    
    def __init__(self, sequences, labels, adjacency):
        self.sequences = sequences
        self.labels = labels
        self.adjacency = adjacency
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]
        
        # Extract features with memory efficiency
        expression = np.array(sequence['expression'], dtype=np.float32)
        current_sledai = float(sequence['current_sledai'])
        
                gene_expression = torch.FloatTensor(expression).unsqueeze(0)  # [1, n_genes]
        
        # Enhanced clinical features
        clinical_features = [
            current_sledai,
            sequence.get('next_sledai', current_sledai) - current_sledai,
            float(sequence.get('current_flare', 0)),
            float(sequence['visit_to'] - sequence['visit_from']),
            current_sledai / 20.0,
            1.0 if current_sledai > 10 else 0.0,
            np.log1p(current_sledai),
            np.sqrt(max(0, current_sledai)),
            (current_sledai ** 2) / 400.0,
            float(hash(sequence.get('patient_id', 'UNKNOWN')) % 100) / 100.0,
            float(sequence.get('visit_from', 0)) / 10.0,
            float(sequence.get('visit_to', 1)) / 10.0,
            np.sin(2 * np.pi * sequence.get('visit_from', 0) / 12),
            np.cos(2 * np.pi * sequence.get('visit_from', 0) / 12),
            np.random.normal(0, 0.005)  # Minimal noise
        ]
        
        clinical_tensor = torch.FloatTensor(clinical_features)
        
        return {
            'gene_expression': gene_expression,
            'clinical_features': clinical_tensor,
            'label': torch.FloatTensor([label])
        }

class CrossValidator:
    """Cross-validation class for optimized TAGT model."""
    
    def __init__(self, config_path: str):
        # Load configuration
        with open(config_path) as f:
            self.config = json.load(f)
        
        self.device = torch.device('cpu')
        logger.info(f"Using device: {self.device}")
        
        # Training parameters
        training_config = self.config.get('training', {})
        self.batch_size = training_config.get('batch_size', 4)
        self.learning_rate = training_config.get('learning_rate', 1e-4)
        self.weight_decay = training_config.get('weight_decay', 1e-5)
        self.epochs = 20  # Reduced for cross-validation
        
        # Cross-validation parameters
        cv_config = self.config.get('cross_validation', {})
        self.n_splits = cv_config.get('n_splits', 5)
        self.shuffle = cv_config.get('shuffle', True)
        self.random_state = cv_config.get('random_state', 42)
        
        logger.info(f"CV Config - Folds: {self.n_splits}, Epochs per fold: {self.epochs}")
        
    def load_data(self):
        """Load real processed data efficiently."""
        try:
            logger.info("Loading real data for cross-validation...")
            
            with open('data/integrated/sequences_real.pkl', 'rb') as f:
                sequences = pickle.load(f)
            
            labels = np.load('data/integrated/labels_real.npy')
            
            # Load adjacency matrix efficiently
            adjacency_sparse = sp.load_npz('data/processed/adjacency_real.npz')
            adjacency = adjacency_sparse.toarray().astype(np.float32)
            
            logger.info(f"Loaded real data: {len(sequences)} sequences")
            logger.info(f"Adjacency shape: {adjacency.shape}")
            logger.info(f"Flare rate: {np.mean(labels):.2%}")
            
            return OptimizedSLEDataset(sequences, labels, adjacency)
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def evaluate_model(self, model, test_loader, adjacency_tensor):
        """Evaluate model performance."""
        model.eval()
        all_labels = []
        all_preds = []
        
        with torch.no_grad():
            for batch in test_loader:
                gene_expression = batch['gene_expression'].to(self.device)
                clinical_features = batch['clinical_features'].to(self.device)
                labels = batch['label'].to(self.device)
                
                outputs = model(gene_expression, adjacency_tensor, clinical_features)
                
                all_labels.extend(labels.cpu().numpy().flatten())
                all_preds.extend(outputs['probabilities'].cpu().numpy().flatten())
        
        # Calculate metrics
        all_labels = np.array(all_labels)
        all_preds = np.array(all_preds)
        
        if len(np.unique(all_labels)) > 1:
            auc = roc_auc_score(all_labels, all_preds)
            
            # Binary predictions with optimal threshold
            threshold = 0.5
            binary_preds = (all_preds > threshold).astype(int)
            
            accuracy = accuracy_score(all_labels, binary_preds)
            precision = precision_score(all_labels, binary_preds, zero_division=0)
            recall = recall_score(all_labels, binary_preds, zero_division=0)
            f1 = f1_score(all_labels, binary_preds, zero_division=0)
        else:
            auc = accuracy = precision = recall = f1 = 0.0
        
        return {
            'auc': auc,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    def run_cross_validation(self):
        """Perform comprehensive cross-validation."""
        logger.info("STARTING CROSS-VALIDATION FOR OPTIMIZED TAGT MODEL")
        logger.info("=" * 60)
        
        # Load data
        full_dataset = self.load_data()
        
        # Set up cross-validation
        kfold = StratifiedKFold(
            n_splits=self.n_splits,
            shuffle=self.shuffle,
            random_state=self.random_state
        )
        
        # Results storage
        cv_results = {
            'auc': [],
            'accuracy': [],
            'precision': [],
            'recall': [],
            'f1': []
        }
        
        adjacency_tensor = torch.FloatTensor(full_dataset.adjacency).to(self.device)
        
        # Cross-validation loop
        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset.sequences, full_dataset.labels)):
            logger.info(f"FOLD {fold + 1}/{self.n_splits}")
            logger.info("-" * 40)
            
                        train_dataset = torch.utils.data.Subset(full_dataset, train_idx)
            val_dataset = torch.utils.data.Subset(full_dataset, val_idx)
            
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.batch_size,
                shuffle=True,
                num_workers=0,
                pin_memory=False
            )
            
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=0,
                pin_memory=False
            )
            
            logger.info(f"Fold {fold + 1}: Train={len(train_dataset)}, Val={len(val_dataset)}")
            
                        model = create_optimized_model(self.config)
            model.to(self.device)
            
            optimizer = optim.AdamW(
                model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
            
            criterion = nn.BCEWithLogitsLoss()
            
            # Training loop for this fold
            best_fold_auc = 0.0
            
            for epoch in range(self.epochs):
                model.train()
                total_loss = 0.0
                num_batches = 0
                
                for batch in train_loader:
                    gene_expression = batch['gene_expression'].to(self.device)
                    clinical_features = batch['clinical_features'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(gene_expression, adjacency_tensor, clinical_features)
                    loss = criterion(outputs['logits'], labels)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    total_loss += loss.item()
                    num_batches += 1
                
                # Validation
                val_metrics = self.evaluate_model(model, val_loader, adjacency_tensor)
                avg_loss = total_loss / num_batches
                
                if epoch % 5 == 0:
                    logger.info(f"  Epoch {epoch+1}: Loss={avg_loss:.4f}, AUC={val_metrics['auc']:.4f}")
                
                if val_metrics['auc'] > best_fold_auc:
                    best_fold_auc = val_metrics['auc']
                    best_fold_metrics = val_metrics.copy()
            
            # Store fold results
            logger.info(f"Fold {fold + 1} Results:")
            logger.info(f"  AUC: {best_fold_metrics['auc']:.4f}")
            logger.info(f"  Accuracy: {best_fold_metrics['accuracy']:.4f}")
            logger.info(f"  Precision: {best_fold_metrics['precision']:.4f}")
            logger.info(f"  Recall: {best_fold_metrics['recall']:.4f}")
            logger.info(f"  F1: {best_fold_metrics['f1']:.4f}")
            
            for metric in cv_results.keys():
                cv_results[metric].append(best_fold_metrics[metric])
            
            # Memory cleanup
            del model, optimizer
            gc.collect()
        
        # Calculate final statistics
        logger.info("\n" + "=" * 60)
        logger.info("CROSS-VALIDATION RESULTS SUMMARY")
        logger.info("=" * 60)
        
        final_results = {}
        for metric, values in cv_results.items():
            mean_val = np.mean(values)
            std_val = np.std(values)
            final_results[metric] = {
                'mean': mean_val,
                'std': std_val,
                'values': values
            }
            
            logger.info(f"{metric.upper():>10}: {mean_val:.4f} ± {std_val:.4f}")
        
        # Save results
        with open('results/cross_validation_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logger.info("\nCross-validation completed successfully!")
        logger.info("Results saved to 'cross_validation_results.json'")
        
        return final_results

def main():
    """Main cross-validation function."""
    logger.info("CROSS-VALIDATION FOR OPTIMIZED TAGT MODEL")
    logger.info("REAL DATA ONLY - ROBUSTNESS VALIDATION")
    
    # Load optimized config
    config_path = "configs/optimized_tagt_config.json"
    
        cv = CrossValidator(config_path)
    
    # Run cross-validation
    results = cv.run_cross_validation()
    
    # Summary
    auc_mean = results['auc']['mean']
    auc_std = results['auc']['std']
    
    logger.info(f"\nFINAL CV AUC: {auc_mean:.4f} ± {auc_std:.4f}")
    
    if auc_mean > 0.95:
        logger.info("EXCELLENT! Model shows consistent high performance!")
    elif auc_mean > 0.90:
        logger.info("VERY GOOD! Model is robust and reliable!")
    elif auc_mean > 0.85:
        logger.info("GOOD! Model shows promise!")
    else:
        logger.info("Model may need further optimization.")

if __name__ == "__main__":
    main()