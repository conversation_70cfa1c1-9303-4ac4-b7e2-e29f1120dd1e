import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Optional, Tuple, Dict, List
from torch.nn import MultiheadAttention, LayerNorm, Dropout

class AdvancedGraphAttentionLayer(nn.Module):
    """Advanced Graph Attention with edge features and multi-scale processing."""
    
    def __init__(self, in_features: int, out_features: int, num_heads: int = 8, 
                 dropout: float = 0.1, alpha: float = 0.2, edge_dim: int = 1):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.num_heads = num_heads
        self.head_dim = out_features // num_heads
        self.alpha = alpha
        
        assert out_features % num_heads == 0
        
        # Multi-head projections
        self.W = nn.Linear(in_features, out_features, bias=False)
        self.a = nn.Parameter(torch.empty(size=(2 * self.head_dim, 1)))
        
        # Edge feature processing
        self.edge_encoder = nn.Sequential(
            nn.Linear(edge_dim, self.head_dim),
            nn.ReLU(),
            nn.Linear(self.head_dim, self.head_dim)
        )
        
        # Multi-scale convolutions for richer representations
        self.conv1x1 = nn.Conv1d(in_features, out_features // 4, 1)
        self.conv3x1 = nn.Conv1d(in_features, out_features // 4, 3, padding=1)
        self.conv5x1 = nn.Conv1d(in_features, out_features // 4, 5, padding=2)
        self.conv7x1 = nn.Conv1d(in_features, out_features // 4, 7, padding=3)
        
        # Output processing
        self.dropout = nn.Dropout(dropout)
        self.leakyrelu = nn.LeakyReLU(self.alpha)
        self.layer_norm = LayerNorm(out_features)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        nn.init.xavier_uniform_(self.W.weight)
        nn.init.xavier_uniform_(self.a)
    
    def forward(self, h, adj, edge_features=None):
        """
        Args:
            h: [batch_size, N, in_features] - Node features
            adj: [batch_size, N, N] - Adjacency matrix
            edge_features: [batch_size, N, N, edge_dim] - Edge features
        """
        batch_size, N, _ = h.shape
        
        # Multi-scale feature extraction
        h_conv = h.transpose(1, 2)  # [batch_size, in_features, N]
        conv_1 = self.conv1x1(h_conv)
        conv_3 = self.conv3x1(h_conv)
        conv_5 = self.conv5x1(h_conv)
        conv_7 = self.conv7x1(h_conv)
        
        # Combine multi-scale features
        h_multiscale = torch.cat([conv_1, conv_3, conv_5, conv_7], dim=1)
        h_multiscale = h_multiscale.transpose(1, 2)  # [batch_size, N, out_features]
        
        # Standard attention computation
        Wh = self.W(h)  # [batch_size, N, out_features]
        Wh = Wh.view(batch_size, N, self.num_heads, self.head_dim)
        
        # Compute attention coefficients
        a_input = self._prepare_attentional_mechanism_input(Wh)
        e = self.leakyrelu(torch.matmul(a_input, self.a).squeeze(-1))
        
        # Add edge features if available
        if edge_features is not None:
            edge_encoded = self.edge_encoder(edge_features)
            edge_attention = edge_encoded.sum(dim=-1)  # [batch_size, N, N]
            e = e + edge_attention.unsqueeze(2).expand(-1, -1, self.num_heads)
        
        # Apply adjacency mask
        zero_vec = -9e15 * torch.ones_like(e)
        attention = torch.where(adj.unsqueeze(-1) > 0, e, zero_vec)
        attention = F.softmax(attention, dim=1)
        attention = self.dropout(attention)
        
        # Apply attention to values
        h_prime = torch.matmul(attention.transpose(1, 2), Wh)
        h_prime = h_prime.transpose(1, 2).contiguous().view(batch_size, N, self.out_features)
        
        # Combine with multi-scale features
        h_combined = h_prime + h_multiscale
        
        # Residual connection and layer norm
        if self.in_features == self.out_features:
            h_combined = h_combined + h
        
        return self.layer_norm(h_combined)
    
    def _prepare_attentional_mechanism_input(self, Wh):
        batch_size, N, num_heads, head_dim = Wh.shape
        Wh_repeated_in_chunks = Wh.repeat_interleave(N, dim=1)
        Wh_repeated_alternating = Wh.repeat(1, N, 1, 1)
        
        all_combinations_matrix = torch.cat([Wh_repeated_in_chunks, Wh_repeated_alternating], dim=-1)
        return all_combinations_matrix.view(batch_size, N, N, num_heads, 2 * head_dim)

class PathwayAwareAttention(nn.Module):
    """Pathway-aware attention for biological interpretability."""
    
    def __init__(self, gene_dim: int, pathway_dim: int = 128, num_pathways: int = 100):
        super().__init__()
        self.gene_dim = gene_dim
        self.pathway_dim = pathway_dim
        self.num_pathways = num_pathways
        
        # Learnable pathway embeddings
        self.pathway_embeddings = nn.Parameter(torch.randn(num_pathways, pathway_dim))
        
        # Gene-to-pathway mapping
        self.gene_to_pathway = nn.Linear(gene_dim, pathway_dim)
        
        # Multi-head attention for pathway interactions
        self.pathway_attention = MultiheadAttention(
            embed_dim=pathway_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Output projection
        self.output_projection = nn.Linear(pathway_dim, gene_dim)
        
    def forward(self, gene_features):
        """
        Args:
            gene_features: [batch_size, num_genes, gene_dim]
        Returns:
            pathway_features: [batch_size, num_genes, gene_dim]
            attention_weights: [batch_size, num_genes, num_pathways]
        """
        batch_size, num_genes, _ = gene_features.shape
        
        # Project genes to pathway space
        gene_pathway_proj = self.gene_to_pathway(gene_features)
        
        # Expand pathway embeddings for batch
        pathway_emb = self.pathway_embeddings.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Cross-attention: genes attend to pathways
        attended_pathways, attention_weights = self.pathway_attention(
            query=gene_pathway_proj,
            key=pathway_emb,
            value=pathway_emb
        )
        
        # Project back to gene space
        pathway_enhanced_genes = self.output_projection(attended_pathways)
        
        return pathway_enhanced_genes, attention_weights

class HierarchicalTemporalEncoder(nn.Module):
    """Hierarchical temporal encoding with multiple time scales."""
    
    def __init__(self, input_dim: int, hidden_dim: int = 256, num_layers: int = 2):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # Multi-scale LSTM encoders
        self.short_term_lstm = nn.LSTM(
            input_dim, hidden_dim // 2, num_layers,
            batch_first=True, bidirectional=True, dropout=0.1
        )
        
        self.medium_term_lstm = nn.LSTM(
            input_dim, hidden_dim // 2, num_layers,
            batch_first=True, bidirectional=True, dropout=0.1
        )
        
        self.long_term_lstm = nn.LSTM(
            input_dim, hidden_dim // 2, num_layers,
            batch_first=True, bidirectional=True, dropout=0.1
        )
        
        # Temporal fusion attention
        self.temporal_fusion = MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Output projection
        self.output_proj = nn.Linear(hidden_dim, input_dim)
        
    def forward(self, x, lengths=None):
        """
        Args:
            x: [batch_size, seq_len, input_dim]
            lengths: Actual sequence lengths
        """
        batch_size, seq_len, _ = x.shape
        
        # Short-term processing (full resolution)
        short_out, _ = self.short_term_lstm(x)
        
        # Medium-term processing (downsampled by 2)
        if seq_len > 1:
            medium_x = x[:, ::2, :]
            medium_out, _ = self.medium_term_lstm(medium_x)
            # Upsample back to original length
            medium_out = F.interpolate(
                medium_out.transpose(1, 2), 
                size=seq_len, 
                mode='linear', 
                align_corners=False
            ).transpose(1, 2)
        else:
            medium_out = short_out
        
        # Long-term processing (downsampled by 4)
        if seq_len > 3:
            long_x = x[:, ::4, :]
            long_out, _ = self.long_term_lstm(long_x)
            # Upsample back to original length
            long_out = F.interpolate(
                long_out.transpose(1, 2), 
                size=seq_len, 
                mode='linear', 
                align_corners=False
            ).transpose(1, 2)
        else:
            long_out = short_out
        
        # Combine multi-scale features
        multi_scale_features = torch.stack([short_out, medium_out, long_out], dim=2)
        multi_scale_features = multi_scale_features.view(batch_size, seq_len * 3, self.hidden_dim)
        
        # Apply temporal fusion attention
        fused_features, _ = self.temporal_fusion(
            multi_scale_features, multi_scale_features, multi_scale_features
        )
        
        # Reshape and aggregate
        fused_features = fused_features.view(batch_size, seq_len, 3, self.hidden_dim)
        temporal_output = fused_features.mean(dim=2)  # Average across scales
        
        # Project to output
        output = self.output_proj(temporal_output)
        
        return output

class CrossModalFusion(nn.Module):
    """Advanced cross-modal fusion for genomic and clinical data."""
    
    def __init__(self, genomic_dim: int, clinical_dim: int, fusion_dim: int = 256):
        super().__init__()
        self.genomic_dim = genomic_dim
        self.clinical_dim = clinical_dim
        self.fusion_dim = fusion_dim
        
        # Modality-specific encoders
        self.genomic_encoder = nn.Sequential(
            nn.Linear(genomic_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.clinical_encoder = nn.Sequential(
            nn.Linear(clinical_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # Cross-modal attention
        self.cross_attention = MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        # Fusion layers
        self.fusion_layers = nn.Sequential(
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, genomic_features, clinical_features):
        """
        Args:
            genomic_features: [batch_size, seq_len, genomic_dim]
            clinical_features: [batch_size, clinical_dim]
        """
        batch_size, seq_len, _ = genomic_features.shape
        
        # Encode modalities
        genomic_encoded = self.genomic_encoder(genomic_features)
        clinical_encoded = self.clinical_encoder(clinical_features)
        
        # Expand clinical features to match sequence length
        clinical_expanded = clinical_encoded.unsqueeze(1).expand(-1, seq_len, -1)
        
        # Cross-modal attention
        genomic_attended, _ = self.cross_attention(
            query=genomic_encoded,
            key=clinical_expanded,
            value=clinical_expanded
        )
        
        clinical_attended, _ = self.cross_attention(
            query=clinical_expanded,
            key=genomic_encoded,
            value=genomic_encoded
        )
        
        # Fuse attended features
        fused = torch.cat([genomic_attended, clinical_attended], dim=-1)
        output = self.fusion_layers(fused)
        
        return output

class UltimateTAGT(nn.Module):
    """🚀 The Ultimate TAGT Model - Real Data Only!"""
    
    def __init__(self, 
                 n_genes: int = 1000,
                 hidden_dim: int = 512,
                 num_heads: int = 16,
                 num_layers: int = 6,
                 clinical_dim: int = 15,
                 dropout: float = 0.1,
                 num_pathways: int = 100):
        super().__init__()
        
        self.n_genes = n_genes
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        
        # Gene expression embedding
        self.gene_embedding = nn.Sequential(
            nn.Linear(1, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim)
        )
        
        # Positional encoding for genes
        self.positional_encoding = nn.Parameter(torch.randn(n_genes, hidden_dim))
        
        # Multi-layer graph attention
        self.graph_layers = nn.ModuleList([
            AdvancedGraphAttentionLayer(
                in_features=hidden_dim,
                out_features=hidden_dim,
                num_heads=num_heads,
                dropout=dropout
            ) for _ in range(num_layers)
        ])
        
        # Pathway-aware attention
        self.pathway_attention = PathwayAwareAttention(
            gene_dim=hidden_dim,
            pathway_dim=hidden_dim // 2,
            num_pathways=num_pathways
        )
        
        # Hierarchical temporal encoder
        self.temporal_encoder = HierarchicalTemporalEncoder(
            input_dim=hidden_dim,
            hidden_dim=hidden_dim
        )
        
        # Cross-modal fusion
        self.cross_modal_fusion = CrossModalFusion(
            genomic_dim=hidden_dim,
            clinical_dim=clinical_dim,
            fusion_dim=hidden_dim
        )
        
        # Final classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Parameter):
            torch.nn.init.normal_(module, std=0.02)
    
    def forward(self, gene_expression, adjacency, clinical_features, edge_features=None):
        """
        Args:
            gene_expression: [batch_size, seq_len, n_genes]
            adjacency: [n_genes, n_genes] or [batch_size, n_genes, n_genes]
            clinical_features: [batch_size, clinical_dim]
            edge_features: [n_genes, n_genes, edge_dim] (optional)
        """
        batch_size, seq_len, n_genes = gene_expression.shape
        
        # Ensure adjacency has batch dimension
        if adjacency.dim() == 2:
            adjacency = adjacency.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Process each time step
        temporal_features = []
        pathway_attentions = []
        
        for t in range(seq_len):
            # Get gene expression at time t
            x_t = gene_expression[:, t, :]  # [batch_size, n_genes]
            
            # Embed gene expressions
            x_embedded = self.gene_embedding(x_t.unsqueeze(-1))  # [batch_size, n_genes, hidden_dim]
            
            # Add positional encoding
            x_embedded = x_embedded + self.positional_encoding.unsqueeze(0)
            
            # Apply graph attention layers
            for layer in self.graph_layers:
                x_embedded = layer(x_embedded, adjacency, edge_features)
            
            # Apply pathway attention
            pathway_enhanced, pathway_attn = self.pathway_attention(x_embedded)
            pathway_attentions.append(pathway_attn)
            
            # Pool gene features to get sequence representation
            sequence_repr = pathway_enhanced.mean(dim=1)  # [batch_size, hidden_dim]
            temporal_features.append(sequence_repr)
        
        # Stack temporal features
        temporal_sequence = torch.stack(temporal_features, dim=1)  # [batch_size, seq_len, hidden_dim]
        
        # Apply hierarchical temporal encoding
        temporal_encoded = self.temporal_encoder(temporal_sequence)
        
        # Cross-modal fusion with clinical data
        fused_features = self.cross_modal_fusion(temporal_encoded, clinical_features)
        
        # Final prediction (use last time step)
        final_features = fused_features[:, -1, :]  # [batch_size, hidden_dim]
        logits = self.classifier(final_features)
        
        return {
            'logits': logits,
            'probabilities': torch.sigmoid(logits),
            'temporal_features': temporal_encoded,
            'pathway_attention': torch.stack(pathway_attentions, dim=1),
            'final_features': final_features
        }

def create_ultimate_model(config: Dict) -> UltimateTAGT:
    """Create the ultimate TAGT model from configuration."""
    return UltimateTAGT(
        n_genes=config.get('n_genes', 1000),
        hidden_dim=config.get('hidden_dim', 512),
        num_heads=config.get('num_heads', 16),
        num_layers=config.get('num_layers', 6),
        clinical_dim=config.get('clinical_dim', 15),
        dropout=config.get('dropout', 0.1),
        num_pathways=config.get('num_pathways', 100)
    )

if __name__ == "__main__":
    # Test the ultimate model
    config = {
        'n_genes': 1000,
        'hidden_dim': 512,
        'num_heads': 16,
        'num_layers': 6,
        'clinical_dim': 15,
        'dropout': 0.1,
        'num_pathways': 100
    }
    
    model = create_ultimate_model(config)
    
    # Test forward pass
    batch_size, seq_len, n_genes = 4, 3, 1000
    
    gene_expression = torch.randn(batch_size, seq_len, n_genes)
    adjacency = torch.rand(n_genes, n_genes)
    adjacency = (adjacency + adjacency.T) / 2  # Make symmetric
    clinical_features = torch.randn(batch_size, 15)
    
    output = model(gene_expression, adjacency, clinical_features)
    
    print("🚀 ULTIMATE TAGT MODEL READY!")
    print(f"Model output shapes:")
    for key, value in output.items():
        print(f"  {key}: {value.shape}")
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"\nTotal parameters: {total_params:,}")
    print("REAL DATA ONLY - NO COMPROMISES! 💪")