name: Code Quality Assurance

# Trigger the workflow on push and pull requests
on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'tests/**'
      - 'requirements.txt'
      - '.github/workflows/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'tests/**'
      - 'requirements.txt'
      - '.github/workflows/**'
  schedule:
    # Run weekly security scans
    - cron: '0 2 * * 1'
  workflow_dispatch:
    # Allow manual triggering

jobs:
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y graphviz
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install black isort flake8 mypy bandit safety pytest pytest-cov
        pip install vulture radon autopep8 pre-commit sphinx
    
    - name: Cache quality tools
      uses: actions/cache@v3
      with:
        path: ~/.cache/pre-commit
        key: pre-commit-${{ runner.os }}-${{ hashFiles('.pre-commit-config.yaml') }}
    
    - name: Set up pre-commit
      run: |
        pre-commit install
        pre-commit run --all-files || true
    
    - name: Code formatting check
      run: |
        echo "Checking code formatting..."
        black --check --diff src/
        isort --check-only --diff src/
    
    - name: Linting analysis
      run: |
        echo "Running linting analysis..."
        flake8 src/ --max-line-length=88 --extend-ignore=E203,W503 --statistics
    
    - name: Type checking
      run: |
        echo "Running type checking..."
        mypy src/ --ignore-missing-imports --strict-optional || true
    
    - name: Security scanning
      run: |
        echo "Running security scans..."
        bandit -r src/ -f json -o bandit-report.json || true
        safety check --json --output safety-report.json || true
    
    - name: Code complexity analysis
      run: |
        echo "Analyzing code complexity..."
        radon cc src/ --json > complexity-report.json || true
        radon mi src/ --json > maintainability-report.json || true
    
    - name: Dead code detection
      run: |
        echo "Detecting dead code..."
        vulture src/ > dead-code-report.txt || true
    
    - name: Run tests with coverage
      run: |
        echo "Running tests with coverage..."
        pytest tests/ --cov=src --cov-report=xml --cov-report=html --cov-report=term
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
    
    - name: Generate quality report
      run: |
        echo "Generating comprehensive quality report..."
        python -c "
import json
import os
from datetime import datetime

# Collect all reports
reports = {
    'timestamp': datetime.now().isoformat(),
    'python_version': '${{ matrix.python-version }}',
    'runner_os': '${{ runner.os }}',
    'reports_generated': []
}

# Check which reports were generated
report_files = [
    'bandit-report.json',
    'safety-report.json', 
    'complexity-report.json',
    'maintainability-report.json',
    'dead-code-report.txt',
    'coverage.xml'
]

for report_file in report_files:
    if os.path.exists(report_file):
        reports['reports_generated'].append({
            'file': report_file,
            'size': os.path.getsize(report_file)
        })

with open('quality-summary.json', 'w') as f:
    json.dump(reports, f, indent=2)

print('Quality analysis completed')
"
    
    - name: Upload quality reports
      uses: actions/upload-artifact@v3
      with:
        name: quality-reports-py${{ matrix.python-version }}
        path: |
          bandit-report.json
          safety-report.json
          complexity-report.json
          maintainability-report.json
          dead-code-report.txt
          htmlcov/
          quality-summary.json
        retention-days: 30
    
    - name: Comment PR with quality summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          try {
            const summary = JSON.parse(fs.readFileSync('quality-summary.json', 'utf8'));
            
            const comment = `## 🔍 Code Quality Analysis Summary
            
            **Python Version:** ${{ matrix.python-version }}
            **Timestamp:** ${summary.timestamp}
            
            ### 📊 Generated Reports
            ${summary.reports_generated.map(r => `- ✅ ${r.file} (${r.size} bytes)`).join('\n')}
            
            ### 🎯 Quality Metrics
            - Code formatting: Black + isort
            - Linting: Flake8
            - Type checking: MyPy
            - Security: Bandit + Safety
            - Complexity: Radon
            - Test coverage: Pytest-cov
            
            📁 Detailed reports are available in the workflow artifacts.
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.log('Could not create PR comment:', error);
          }

  gpu-optimization:
    name: GPU Optimization Analysis
    runs-on: ubuntu-latest
    if: contains(github.event.head_commit.message, '[gpu]') || github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
        pip install -r requirements.txt
    
    - name: GPU optimization analysis
      run: |
        echo "Analyzing GPU optimization opportunities..."
        python -c "
import torch
import os
import re
from pathlib import Path

print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())

# Analyze Python files for GPU optimization opportunities
src_files = list(Path('src').rglob('*.py'))
optimization_suggestions = []

for file_path in src_files:
    with open(file_path, 'r') as f:
        content = f.read()
    
    suggestions = []
    
    # Check for device placement
    if 'torch' in content and '.to(device)' not in content and '.cuda()' not in content:
        suggestions.append('Consider adding device placement (.to(device))')
    
    # Check for mixed precision
    if 'autocast' not in content and 'GradScaler' not in content and 'torch.nn' in content:
        suggestions.append('Consider using mixed precision training (autocast + GradScaler)')
    
    # Check for DataLoader optimization
    if 'DataLoader' in content:
        if 'num_workers' not in content:
            suggestions.append('Consider setting num_workers in DataLoader')
        if 'pin_memory' not in content:
            suggestions.append('Consider enabling pin_memory in DataLoader')
    
    if suggestions:
        optimization_suggestions.append({
            'file': str(file_path),
            'suggestions': suggestions
        })

if optimization_suggestions:
    print('\n🚀 GPU Optimization Suggestions:')
    for item in optimization_suggestions:
        print(f'\n📁 {item["file"]}:')
        for suggestion in item['suggestions']:
            print(f'  • {suggestion}')
else:
    print('✅ No obvious GPU optimization opportunities found')
"
    
    - name: Create GPU optimization report
      run: |
        echo "# GPU Optimization Report" > gpu-optimization-report.md
        echo "" >> gpu-optimization-report.md
        echo "Generated on: $(date)" >> gpu-optimization-report.md
        echo "" >> gpu-optimization-report.md
        echo "## Recommendations" >> gpu-optimization-report.md
        echo "" >> gpu-optimization-report.md
        echo "- Use mixed precision training with autocast and GradScaler" >> gpu-optimization-report.md
        echo "- Optimize DataLoader with num_workers and pin_memory" >> gpu-optimization-report.md
        echo "- Ensure proper device placement for tensors and models" >> gpu-optimization-report.md
        echo "- Consider gradient accumulation for large batch sizes" >> gpu-optimization-report.md
        echo "- Use torch.compile() for PyTorch 2.0+ optimization" >> gpu-optimization-report.md
    
    - name: Upload GPU optimization report
      uses: actions/upload-artifact@v3
      with:
        name: gpu-optimization-report
        path: gpu-optimization-report.md
        retention-days: 30

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[security]')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety semgrep
        pip install -r requirements.txt
    
    - name: Run comprehensive security scan
      run: |
        echo "Running comprehensive security analysis..."
        
        # Bandit security scan
        bandit -r src/ -f json -o bandit-security.json || true
        
        # Safety dependency check
        safety check --json --output safety-security.json || true
        
        # Semgrep security patterns
        semgrep --config=auto src/ --json --output=semgrep-security.json || true
    
    - name: Generate security summary
      run: |
        python -c "
import json
import os
from datetime import datetime

security_summary = {
    'timestamp': datetime.now().isoformat(),
    'scans_completed': [],
    'total_issues': 0
}

# Process Bandit results
if os.path.exists('bandit-security.json'):
    with open('bandit-security.json', 'r') as f:
        bandit_data = json.load(f)
    security_summary['scans_completed'].append('bandit')
    security_summary['bandit_issues'] = len(bandit_data.get('results', []))
    security_summary['total_issues'] += security_summary['bandit_issues']

# Process Safety results
if os.path.exists('safety-security.json'):
    with open('safety-security.json', 'r') as f:
        safety_data = json.load(f)
    security_summary['scans_completed'].append('safety')
    security_summary['safety_vulnerabilities'] = len(safety_data)
    security_summary['total_issues'] += security_summary['safety_vulnerabilities']

# Process Semgrep results
if os.path.exists('semgrep-security.json'):
    with open('semgrep-security.json', 'r') as f:
        semgrep_data = json.load(f)
    security_summary['scans_completed'].append('semgrep')
    security_summary['semgrep_findings'] = len(semgrep_data.get('results', []))
    security_summary['total_issues'] += security_summary['semgrep_findings']

with open('security-summary.json', 'w') as f:
    json.dump(security_summary, f, indent=2)

print(f'Security audit completed. Total issues found: {security_summary["total_issues"]}')
"
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-audit-reports
        path: |
          bandit-security.json
          safety-security.json
          semgrep-security.json
          security-summary.json
        retention-days: 90
    
    - name: Fail on critical security issues
      run: |
        python -c "
import json
import sys

with open('security-summary.json', 'r') as f:
    summary = json.load(f)

if summary['total_issues'] > 10:
    print(f'❌ Too many security issues found: {summary["total_issues"]}')
    sys.exit(1)
else:
    print(f'✅ Security check passed. Issues found: {summary["total_issues"]}')
"

  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [code-quality]
    if: always()
    
    steps:
    - name: Download quality reports
      uses: actions/download-artifact@v3
      with:
        name: quality-reports-py3.9
        path: quality-reports/
    
    - name: Evaluate quality gate
      run: |
        echo "Evaluating quality gate criteria..."
        
        # Set quality thresholds
        MAX_COMPLEXITY=10
        MIN_COVERAGE=70
        MAX_SECURITY_ISSUES=5
        
        # Initialize gate status
        GATE_PASSED=true
        
        echo "Quality Gate Evaluation:"
        echo "========================"
        
        # Check if reports exist and evaluate
        if [ -f "quality-reports/quality-summary.json" ]; then
          echo "✅ Quality reports found"
        else
          echo "❌ Quality reports missing"
          GATE_PASSED=false
        fi
        
        # Summary
        if [ "$GATE_PASSED" = true ]; then
          echo "\n🎉 Quality Gate: PASSED"
          echo "All quality criteria met!"
        else
          echo "\n❌ Quality Gate: FAILED"
          echo "Quality criteria not met. Please review the issues."
          exit 1
        fi
    
    - name: Create quality badge
      run: |
        # Create a simple quality status badge
        echo '{"schemaVersion": 1, "label": "quality", "message": "passing", "color": "green"}' > quality-badge.json
    
    - name: Upload quality badge
      uses: actions/upload-artifact@v3
      with:
        name: quality-badge
        path: quality-badge.json
        retention-days: 30