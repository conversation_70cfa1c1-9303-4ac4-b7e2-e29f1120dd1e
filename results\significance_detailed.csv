# Statistical Significance Test Results
# Generated by significance.py

## auc_bootstrap
                              baseline_logistic_regression  baseline_lstm  ...    tagt  tagt_cv
baseline_logistic_regression                        1.0000         0.3014  ...  0.0008   0.0000
baseline_lstm                                       0.3014         1.0000  ...  0.0000   0.0000
baseline_random_forest                              0.1292         0.8952  ...  0.0128   0.0000
baseline_svm                                        0.0336         0.6090  ...  0.0008   0.0000
tagt                                                0.0008         0.0000  ...  1.0000   0.6544
tagt_cv                                             0.0000         0.0000  ...  0.6544   1.0000

[6 rows x 6 columns]

## acc_bootstrap
                              baseline_logistic_regression  baseline_lstm  ...    tagt  tagt_cv
baseline_logistic_regression                        1.0000            0.0  ...  0.0008   0.0000
baseline_lstm                                       0.0000            1.0  ...  0.0000   0.0000
baseline_random_forest                              0.0228            0.0  ...  0.0128   0.0000
baseline_svm                                        0.0228            0.0  ...  0.0128   0.0000
tagt                                                0.0008            0.0  ...  1.0000   0.4542
tagt_cv                                             0.0000            0.0  ...  0.4542   1.0000

[6 rows x 6 columns]

## f1_bootstrap
                              baseline_logistic_regression  baseline_lstm  ...    tagt  tagt_cv
baseline_logistic_regression                        1.0000         0.1134  ...  0.0008   0.0000
baseline_lstm                                       0.1134         1.0000  ...  0.0000   0.0000
baseline_random_forest                              0.0166         0.0008  ...  0.0000   0.0000
baseline_svm                                        0.0166         0.0008  ...  0.0000   0.0000
tagt                                                0.0008         0.0000  ...  1.0000   0.8424
tagt_cv                                             0.0000         0.0000  ...  0.8424   1.0000

[6 rows x 6 columns]

## prec_bootstrap
                              baseline_logistic_regression  baseline_lstm  ...    tagt  tagt_cv
baseline_logistic_regression                        1.0000         0.7126  ...  0.0038   0.0000
baseline_lstm                                       0.7126         1.0000  ...  0.0000   0.0000
baseline_random_forest                              0.0166         0.0008  ...  0.0000   0.0000
baseline_svm                                        0.0166         0.0008  ...  0.0000   0.0000
tagt                                                0.0038         0.0000  ...  1.0000   0.6544
tagt_cv                                             0.0000         0.0000  ...  0.6544   1.0000

[6 rows x 6 columns]

## recall_bootstrap
                              baseline_logistic_regression  baseline_lstm  ...    tagt  tagt_cv
baseline_logistic_regression                        1.0000         0.0298  ...  0.0008   0.0000
baseline_lstm                                       0.0298         1.0000  ...  0.0000   0.0000
baseline_random_forest                              0.0166         0.0008  ...  0.0000   0.0000
baseline_svm                                        0.0166         0.0008  ...  0.0000   0.0000
tagt                                                0.0008         0.0000  ...  1.0000   0.5604
tagt_cv                                             0.0000         0.0000  ...  0.5604   1.0000

[6 rows x 6 columns]

