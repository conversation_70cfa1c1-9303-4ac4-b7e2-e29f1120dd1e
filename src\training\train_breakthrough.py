#!/usr/bin/env python3
"""
Breakthrough Training Script for TAGT-SLE Model
Novel SLE Flare Prediction with Uncertainty Quantification
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import os
import gc
from tqdm import tqdm
import json
from pathlib import Path
import logging
import optuna
from train_clean_fixed import TAGT_SLE_Model, UncertaintyAwareLoss

# Configuration
logging.basicConfig(level=logging.INFO)
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class SLEDatasetFixed(Dataset):
    """Fixed SLE Dataset with proper dimension handling"""
    def __init__(self, expression_data, clinical_data, labels, gene_indices=None):
        self.expression_data = torch.FloatTensor(expression_data)
        self.clinical_data = torch.FloatTensor(clinical_data)
        self.labels = torch.LongTensor(labels)
        self.gene_indices = gene_indices
        
        # Ensure proper dimensions
        if len(self.expression_data.shape) == 1:
            self.expression_data = self.expression_data.unsqueeze(0)
        if len(self.clinical_data.shape) == 1:
            self.clinical_data = self.clinical_data.unsqueeze(0)
            
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        return {
            'expression': self.expression_data[idx],
            'sledai': self.clinical_data[idx],
            'label': self.labels[idx]
        }

def create_adjacency_matrix(n_genes, sparsity=0.1):
    """Create a sparse adjacency matrix for PPI network"""
    adj = torch.zeros(n_genes, n_genes)
    n_edges = int(n_genes * n_genes * sparsity)
    
    # Random edges for demonstration - replace with real PPI data
    edges = torch.randint(0, n_genes, (n_edges, 2))
    adj[edges[:, 0], edges[:, 1]] = 1
    adj[edges[:, 1], edges[:, 0]] = 1  # Make symmetric