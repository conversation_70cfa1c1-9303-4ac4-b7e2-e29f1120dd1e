{"model_name": "UltimateTAGT", "description": "Ultimate TAGT model using real GSE49454 and STRING PPI data", "model_architecture": {"n_genes": 1000, "hidden_dim": 256, "num_heads": 8, "num_layers": 3, "clinical_dim": 15, "dropout": 0.15, "num_pathways": 100}, "training": {"batch_size": 4, "learning_rate": 0.0001, "weight_decay": 1e-05, "epochs": 30, "use_amp": true, "accumulation_steps": 8, "warmup_epochs": 10, "patience": 15}, "cross_validation": {"n_splits": 5, "shuffle": true, "random_state": 42}, "optimization": {"optimizer": "AdamW", "scheduler": "cosine_annealing", "min_lr": 1e-07, "gradient_clip": 1.0}, "data": {"use_real_data_only": true, "sequences_path": "data/integrated/sequences_real.pkl", "labels_path": "data/integrated/labels_real.npy", "adjacency_path": "data/processed/adjacency_real.npz", "normalize_features": true, "augmentation": {"enabled": true, "noise_level": 0.02, "dropout_prob": 0.05}}, "logging": {"log_level": "INFO", "log_file": "ultimate_training.log", "save_every_n_epochs": 5, "plot_metrics": true}, "hardware": {"gpu": "RTX3050", "mixed_precision": true, "pin_memory": true, "num_workers": 0}, "reproducibility": {"seed": 42, "deterministic": true, "benchmark": false}}